package vn.vnpt.camau.qldt.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import vn.vnpt.camau.qldt.model.SvDongHp;
import vn.vnpt.camau.qldt.model.SvDongHpId;

import java.util.Date;
import java.util.List;

public interface SvDongHpRepository extends CrudRepository<SvDongHp, SvDongHpId> {

    @Query(value = "call LAY_DS_PHI_NO(?1,?2,?3)",nativeQuery = true)
    List<Object[]> LayDsPhiNo(Integer idSinhVien,Integer idNienKhoa, Integer idHocKy);

    @Query(value = "call TRA_CUU_LS_NOP_PHI(?1,?2,?3)",nativeQuery = true)
    List<Object[]> TraCuuLsNopPhi(Integer idSinhVien, Date from, Date to);

    @Query(value = "select ID_SINH_VIEN,MA_SINH_VIEN,HO_TEN,NU,NGAY_SINH, SDT_SINH_VIEN, DIA_CHI,MA_LOP,TEN_LOP, sum(THANH_TIEN) as TONG_TIEN_NO, LA_SV_BAO_LUU \n" +
            "from (\n" +
            "\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH,sv.SDT_SINH_VIEN,\n" +
            "\t\tcase when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n" +
            "\t\tl.MA_LOP,l.TEN_LOP, \n" +
            "\t\t(hp.SO_LUONG * \n" +
            "\t\t\tCASE WHEN sv.LA_SV_BAO_LUU = 1 THEN \n" +
            "\t\t\t\t(SELECT hp2.MUC_HOC_PHI \n" +
            "\t\t\t\tFROM HOC_PHI hp2 \n" +
            "\t\t\t\tWHERE hp2.ID_KHOI = sv.ID_KHOI_GOC \n" +
            "\t\t\t\tAND hp2.ID_HOC_KY = (SELECT MAX(hk2.ID_HOC_KY) \n" +
            "\t\t\t\t\t\t\t\t  FROM HOC_KY hk2 \n" +
            "\t\t\t\t\t\t\t\t  JOIN NIEN_KHOA nk2 ON nk2.ID_NIEN_KHOA = hk2.ID_NIEN_KHOA \n" +
            "\t\t\t\t\t\t\t\t  WHERE (nk2.ID_NIEN_KHOA = ?2 OR -1 = ?2)\n" +
            "\t\t\t\t\t\t\t\t  AND (hk2.ID_HOC_KY = ?3 OR -1 = ?3)\n" +
            "\t\t\t\t\t\t\t\t  AND hk2.HIEN_TAI = 1)\n" +
            "\t\t\t\tLIMIT 1)\n" +
            "\t\t\tELSE hp.MUC_HOC_PHI \n" +
            "\t\tEND) as THANH_TIEN,\n" +
            "\t\thp.ID_HOC_PHI, sv.LA_SV_BAO_LUU\n" +
            "\tfrom SINH_VIEN sv\n" +
            "\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\tleft join HOC_PHI hp on hp.ID_KHOI=k.ID_KHOI\n" +
            "\tLEFT JOIN SV_DONG_HP dhp ON dhp.ID_SINH_VIEN = sv.ID_SINH_VIEN\n" +
            "\t\tAND (CASE \n" +
            "\t\t\tWHEN sv.LA_SV_BAO_LUU = 1 THEN \n" +
            "\t\t\t\t(SELECT hp2.ID_HOC_PHI \n" +
            "\t\t\t\tFROM HOC_PHI hp2 \n" +
            "\t\t\t\tWHERE hp2.ID_KHOI = sv.ID_KHOI_GOC \n" +
            "\t\t\t\tAND hp2.ID_HOC_KY = (SELECT MAX(hk2.ID_HOC_KY) \n" +
            "\t\t\t\t\t\t\t\t  FROM HOC_KY hk2 \n" +
            "\t\t\t\t\t\t\t\t  JOIN NIEN_KHOA nk2 ON nk2.ID_NIEN_KHOA = hk2.ID_NIEN_KHOA \n" +
            "\t\t\t\t\t\t\t\t  WHERE (nk2.ID_NIEN_KHOA = ?2 OR -1 = ?2)\n" +
            "\t\t\t\t\t\t\t\t  AND (hk2.ID_HOC_KY = ?3 OR -1 = ?3)\n" +
            "\t\t\t\t\t\t\t\t  AND hk2.HIEN_TAI = 1)\n" +
            "\t\t\t\tLIMIT 1)\n" +
            "\t\t\tELSE hp.ID_HOC_PHI\n" +
            "\t\tEND) = dhp.ID_HOC_PHI\n" +
            "\tleft join HOC_KY hk on hk.ID_HOC_KY = hp.ID_HOC_KY\n" +
            "\tleft join NIEN_KHOA nk on nk.ID_NIEN_KHOA = hk.ID_NIEN_KHOA\n" +
            "\twhere (dhp.ID_CAN_BO is null or dhp.huy=1)\n" +
            "\t\tand (dhp.NGAY_THU IS NULL OR dhp.HUY = 1)\n" +
            "\t\tand hp.ID_HOC_PHI is not null\n" +
            "\t\tand CASE \n" +
            "\t\t\tWHEN sv.LA_SV_BAO_LUU = 1 THEN \n" +
            "\t\t\t\t(SELECT hp2.MUC_HOC_PHI \n" +
            "\t\t\t\tFROM HOC_PHI hp2 \n" +
            "\t\t\t\tWHERE hp2.ID_KHOI = sv.ID_KHOI_GOC \n" +
            "\t\t\t\tAND hp2.ID_HOC_KY = (SELECT MAX(hk2.ID_HOC_KY) \n" +
            "\t\t\t\t\t\t\t\t  FROM HOC_KY hk2 \n" +
            "\t\t\t\t\t\t\t\t  JOIN NIEN_KHOA nk2 ON nk2.ID_NIEN_KHOA = hk2.ID_NIEN_KHOA \n" +
            "\t\t\t\t\t\t\t\t  WHERE (nk2.ID_NIEN_KHOA = ?2 OR -1 = ?2)\n" +
            "\t\t\t\t\t\t\t\t  AND (hk2.ID_HOC_KY = ?3 OR -1 = ?3)\n" +
            "\t\t\t\t\t\t\t\t  AND hk2.HIEN_TAI = 1)\n" +
            "\t\t\t\tLIMIT 1) > 0\n" +
            "\t\t\tELSE hp.MUC_HOC_PHI > 0\n" +
            "\t\tEND\n" +
            "\t\tand tt.HOAT_DONG=1 \n" +
//            "\t\tand sv.ID_TINH_TRANG <> 1 \n" +
//            "\t\tand sv.ID_TINH_TRANG <> -1 \n" +
            "\t\tand (sv.ID_TINH_TRANG = ?4 or ?4 = -1) \n" +
            "\t\tand (k.ID_NGANH_HOC=?1 or ?1=-1) \n" +
            "\t\tand (nk.ID_NIEN_KHOA=?2 or ?2=-1) \n" +
            "\t\tand CASE \n" +
            "\t\t\tWHEN sv.LA_SV_BAO_LUU = 1 THEN \n" +
            "\t\t\t\t(SELECT hp2.ID_HOC_KY \n" +
            "\t\t\t\tFROM HOC_PHI hp2 \n" +
            "\t\t\t\tWHERE hp2.ID_KHOI = sv.ID_KHOI_GOC \n" +
            "\t\t\t\tAND hp2.ID_HOC_KY = (SELECT MAX(hk2.ID_HOC_KY) \n" +
            "\t\t\t\t\t\t\t\t  FROM HOC_KY hk2 \n" +
            "\t\t\t\t\t\t\t\t  JOIN NIEN_KHOA nk2 ON nk2.ID_NIEN_KHOA = hk2.ID_NIEN_KHOA \n" +
            "\t\t\t\t\t\t\t\t  WHERE (nk2.ID_NIEN_KHOA = ?2 OR -1 = ?2)\n" +
            "\t\t\t\t\t\t\t\t  AND (hk2.ID_HOC_KY = ?3 OR -1 = ?3)\n" +
            "\t\t\t\t\t\t\t\t  AND hk2.HIEN_TAI = 1)\n" +
            "\t\t\t\tLIMIT 1) = ?3 OR -1 = ?3\n" +
            "\t\t\tELSE (hp.ID_HOC_KY = ?3 OR -1 = ?3)\n" +
            "\t\tEND\n" +
            "\t\tand sv.KHOA=0\n" +
            "\t\tand (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5%)\n" +
            "\t\tunion\n" +
            "\t\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH,sv.SDT_SINH_VIEN,\n" +
            "\t\t\t\t case when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n" +
            "\t\t\t\tl.MA_LOP,l.TEN_LOP, if(pk.NGAY_KET_THUC is not null, if(curdate()<pk.NGAY_BAT_DAU, period_diff(date_format(pk.NGAY_KET_THUC,'%Y%m'),date_format(pk.NGAY_BAT_DAU,'%Y%m'))+1,period_diff(date_format(pk.NGAY_KET_THUC,'%Y%m'),date_format(curdate(),'%Y%m'))+1) *pk.DON_GIA, pk.DON_GIA) as THANH_TIEN, pk.ID_PHI_KHAC, sv.LA_SV_BAO_LUU\n" +
            "\t\tfrom SINH_VIEN sv\n" +
            "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\t\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\t\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\t\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\t\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\t\tleft join PHI_KHAC pk on pk.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join SV_DONG_PK dpk on dpk.ID_PHI_KHAC=pk.ID_PHI_KHAC and dpk.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
            "\t\twhere (dpk.ID_CAN_BO is null or dpk.huy=1) and pk.ID_PHI_KHAC is not null and tt.HOAT_DONG=1 and (sv.ID_TINH_TRANG = ?4 or ?4 = -1) and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and sv.KHOA=0 and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5% ) and (pk.NGAY_BAT_DAU is null or pk.NGAY_KET_THUC is null or (pk.NGAY_BAT_DAU is not null and pk.NGAY_KET_THUC is not null and curdate() < pk.NGAY_KET_THUC))\n" +
            "\t\tunion\n" +
            "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH,sv.SDT_SINH_VIEN,\n" +
            "\t\t\t\t case when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n" +
            "\t\t\t\tMA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_HOC_LAI_LT*SO__VHT when ID_LOAI_MON_HOC=2 then GIA_HOC_LAI_TH*SO__VHT else GIA_HOC_LAI_TT*SO__VHT end) as THANH_TIEN, n.ID_NHOM, sv.LA_SV_BAO_LUU \n" +
            "\t\tfrom NHOM n\n" +
            "\t\tjoin MON_HOC mh on n.ID_MON_HOC=mh.ID_MON_HOC\n" +
            "\t\tjoin SV_HOC_NHOM h on n.ID_NHOM=h.ID_NHOM\n" +
            "\t\tjoin SINH_VIEN sv on h.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
            "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\t\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\t\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=n.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
            "\t\tleft join SV_DONG_PHI_HOC_LAI dhphl on sv.ID_SINH_VIEN=dhphl.ID_SINH_VIEN and n.ID_NHOM=dhphl.ID_NHOM\n" +
            "\t\twhere n.HOC_LAI=1 and (dhphl.ID_CAN_BO is null or dhphl.huy=1) and tt.HOAT_DONG=1 and (sv.ID_TINH_TRANG = ?4 or ?4 = -1) and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and sv.KHOA=0 and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5% )\n" +
            "\t\tunion\n" +
            "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH,sv.SDT_SINH_VIEN,\n" +
            "\t\t\t\t case when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n" +
            "\t\t\t\tMA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_THI_LAI_LT when ID_LOAI_MON_HOC=2 then GIA_THI_LAI_TH else GIA_THI_LAI_TT end) as THANH_TIEN, tl.ID_THI_LAI, sv.LA_SV_BAO_LUU \n" +
            "\t\tfrom THI_LAI tl\n" +
            "\t\tjoin SINH_VIEN sv on tl.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
            "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\t\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\t\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=tl.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
            "\t\tjoin MON_HOC mh on mh.ID_MON_HOC=tl.ID_MON_HOC\n" +
            "\t\twhere (tl.ID_CAN_BO is null or tl.huy=1) and tt.HOAT_DONG=1 and (sv.ID_TINH_TRANG = ?4 or ?4 = -1) and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and sv.KHOA=0 and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5% )) as tbl\n" +
            "group by ID_SINH_VIEN,MA_SINH_VIEN,HO_TEN,NU,NGAY_SINH,SDT_SINH_VIEN, DIA_CHI, MA_LOP,TEN_LOP,LA_SV_BAO_LUU",
            nativeQuery = true,
            countQuery = "select count(ID_SINH_VIEN) \n" +
                    "from (\n" +
                    "\t\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH,l.MA_LOP,l.TEN_LOP, hp.MUC_HOC_PHI as DON_GIA,hp.ID_HOC_PHI\n" +
                    "\t\tfrom SINH_VIEN sv\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join HOC_PHI hp on hp.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join SV_DONG_HP dhp on dhp.ID_HOC_PHI=hp.ID_HOC_PHI and dhp.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\twhere (dhp.ID_CAN_BO is null or dhp.huy=1)  and hp.ID_HOC_PHI is not null and hp.MUC_HOC_PHI>0 and tt.HOAT_DONG=1 and (sv.ID_TINH_TRANG = ?4 or ?4 = -1) and sv.KHOA=0 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5% )\n" +
                    "\t\tunion\n" +
                    "\t\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH,l.MA_LOP,l.TEN_LOP, pk.DON_GIA, pk.ID_PHI_KHAC\n" +
                    "\t\tfrom SINH_VIEN sv\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join PHI_KHAC pk on pk.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join SV_DONG_PK dpk on dpk.ID_PHI_KHAC=pk.ID_PHI_KHAC and dpk.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\twhere (dpk.ID_CAN_BO is null or dpk.huy=1) and pk.ID_PHI_KHAC is not null and tt.HOAT_DONG=1 and sv.KHOA=0 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5% )\n" +
                    "\t\tunion\n" +
                    "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH,MA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_HOC_LAI_LT*SO__VHT when ID_LOAI_MON_HOC=2 then GIA_HOC_LAI_TH*SO__VHT else GIA_HOC_LAI_TT*SO__VHT end) as DON_GIA,n.ID_NHOM\n" +
                    "\t\tfrom NHOM n\n" +
                    "\t\tjoin MON_HOC mh on n.ID_MON_HOC=mh.ID_MON_HOC\n" +
                    "\t\tjoin SV_HOC_NHOM h on n.ID_NHOM=h.ID_NHOM\n" +
                    "\t\tjoin SINH_VIEN sv on h.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=n.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
                    "\t\tleft join SV_DONG_PHI_HOC_LAI dhphl on sv.ID_SINH_VIEN=dhphl.ID_SINH_VIEN and n.ID_NHOM=dhphl.ID_NHOM\n" +
                    "\t\twhere n.HOC_LAI=1 and (dhphl.ID_CAN_BO is null or dhphl.huy=1) and tt.HOAT_DONG=1 and sv.KHOA=0 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5% )\n" +
                    "\t\tunion\n" +
                    "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH,MA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_THI_LAI_LT when ID_LOAI_MON_HOC=2 then GIA_THI_LAI_TH else GIA_THI_LAI_TT end) as DON_GIA, tl.ID_THI_LAI\n" +
                    "\t\tfrom THI_LAI tl\n" +
                    "\t\tjoin SINH_VIEN sv on tl.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=tl.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
                    "\t\tjoin MON_HOC mh on mh.ID_MON_HOC=tl.ID_MON_HOC\n" +
                    "\t\twhere (tl.ID_CAN_BO is null or tl.huy=1) and tt.HOAT_DONG=1 and sv.KHOA=0 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?5% or sv.MA_SINH_VIEN LIKE %?5% )) as tbl\n" +
                    "group by ID_SINH_VIEN")
    Page<Object[]> LayDsSinhVienNoPhiPaging(Integer idNganhHoc, Integer idKhoaHoc, Integer idLop, Integer idTinhTrang, String keyword, Pageable page);

    @Query(value = "call LAY_DS_PHI_THI_LAI_MON(?1,?2,?3,?4)",nativeQuery = true)
    List<Object[]> LayDsPhiThiLai(Integer idSinhVien,Integer idNienKhoa, Integer idHocKy, Integer idMonHoc);

	@Query(value = "call LAY_DS_PHI_THU(?1,?2,?3)",nativeQuery = true)
    List<Object[]> LayDsPhiThu(Integer idSinhVien,Integer idNienKhoa, Integer idHocKy);

	@Query(value = "select ID_SINH_VIEN,MA_SINH_VIEN,HO_TEN,NU,NGAY_SINH,SDT_SINH_VIEN, DIA_CHI,MA_LOP,TEN_LOP, sum(DON_GIA) as TONG_TIEN_NO \n" +
            "from (\n" +
            "\t\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH, sv.SDT_SINH_VIEN,\n"+
            "\t\t\t\tcase when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n"+
            "\t\t\t\tl.MA_LOP,l.TEN_LOP, hp.MUC_HOC_PHI as DON_GIA,hp.ID_HOC_PHI\n" +
            "\t\tfrom SINH_VIEN sv\n" +
            "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\t\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\t\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join HOC_PHI hp on hp.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join SV_DONG_HP dhp on dhp.ID_HOC_PHI=hp.ID_HOC_PHI and dhp.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
            "\t\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\t\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\t\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\t\twhere (dhp.ID_CAN_BO is not null and dhp.huy=0) and hp.ID_HOC_PHI is not null and hp.MUC_HOC_PHI>0 and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and sv.KHOA=0 and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )\n" +
            "\t\tunion\n" +
            "\t\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH,sv.SDT_SINH_VIEN, \n"+
            "\t\t\t\tcase when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n"+
            "\t\t\t\tl.MA_LOP,l.TEN_LOP, pk.DON_GIA, pk.ID_PHI_KHAC\n" +
            "\t\tfrom SINH_VIEN sv\n" +
            "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\t\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\t\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join PHI_KHAC pk on pk.ID_KHOI=k.ID_KHOI\n" +
            "\t\tleft join SV_DONG_PK dpk on dpk.ID_PHI_KHAC=pk.ID_PHI_KHAC and dpk.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
            "\t\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\t\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\t\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\t\twhere (dpk.ID_CAN_BO is not null and dpk.huy=0) and pk.ID_PHI_KHAC is not null and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and sv.KHOA=0 and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )\n" +
            "\t\tunion\n" +
            "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH, sv.SDT_SINH_VIEN, \n"+
            "\t\t\t\tcase when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n"+
            "\t\t\t\tMA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_HOC_LAI_LT*SO__VHT when ID_LOAI_MON_HOC=2 then GIA_HOC_LAI_TH*SO__VHT else GIA_HOC_LAI_TT*SO__VHT end) as DON_GIA,n.ID_NHOM\n" +
            "\t\tfrom NHOM n\n" +
            "\t\tjoin MON_HOC mh on n.ID_MON_HOC=mh.ID_MON_HOC\n" +
            "\t\tjoin SV_HOC_NHOM h on n.ID_NHOM=h.ID_NHOM\n" +
            "\t\tjoin SINH_VIEN sv on h.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
            "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=n.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
            "\t\tleft join SV_DONG_PHI_HOC_LAI dhphl on sv.ID_SINH_VIEN=dhphl.ID_SINH_VIEN\n" +
            "\t\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\t\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\t\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\t\twhere n.HOC_LAI=1 and (dhphl.ID_CAN_BO is not null and dhphl.huy=0) and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )\n" +
            "\t\tunion\n" +
            "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH, sv.SDT_SINH_VIEN, \n"+
            "\t\t\t\tcase when sv.ID_XA is null then sv.DIA_CHI else concat(sv.DIA_CHI, ', ' , dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI,\n"+
            "\t\t\t\tMA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_THI_LAI_LT when ID_LOAI_MON_HOC=2 then GIA_THI_LAI_TH else GIA_THI_LAI_TT end) as DON_GIA, tl.ID_THI_LAI\n" +
            "\t\tfrom THI_LAI tl\n" +
            "\t\tjoin SINH_VIEN sv on tl.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
            "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
            "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
            "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
            "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=tl.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
            "\t\tjoin MON_HOC mh on mh.ID_MON_HOC=tl.ID_MON_HOC\n" +
            "\t\tleft join DM_XA dmXa on sv.ID_XA = dmXa.ID_XA\n" +
            "\t\tleft join DM_HUYEN dmHuyen on dmXa.ID_HUYEN = dmHuyen.ID_HUYEN\n" +
            "\t\tleft join DM_TINH dmTinh on dmHuyen.ID_TINH = dmTinh.ID_TINH\n" +
            "\t\twhere (tl.ID_CAN_BO is not null and tl.huy=0) and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )) as tbl\n" +
            "group by ID_SINH_VIEN,MA_SINH_VIEN,HO_TEN,NU,NGAY_SINH, SDT_SINH_VIEN,DIA_CHI, MA_LOP,TEN_LOP",
            nativeQuery = true,
            countQuery = "select count(ID_SINH_VIEN) \n" +
                    "from (\n" +
                    "\t\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH,l.MA_LOP,l.TEN_LOP, hp.MUC_HOC_PHI as DON_GIA,hp.ID_HOC_PHI\n" +
                    "\t\tfrom SINH_VIEN sv\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join HOC_PHI hp on hp.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join SV_DONG_HP dhp on dhp.ID_HOC_PHI=hp.ID_HOC_PHI and dhp.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\twhere (dhp.ID_CAN_BO is not null and dhp.huy=0) and hp.ID_HOC_PHI is not null and hp.MUC_HOC_PHI>0 and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and sv.KHOA=0 and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )\n" +
                    "\t\tunion\n" +
                    "\t\tselect sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,sv.NU,sv.NGAY_SINH,l.MA_LOP,l.TEN_LOP, pk.DON_GIA, pk.ID_PHI_KHAC\n" +
                    "\t\tfrom SINH_VIEN sv\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tleft join LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tleft join KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join PHI_KHAC pk on pk.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tleft join SV_DONG_PK dpk on dpk.ID_PHI_KHAC=pk.ID_PHI_KHAC and dpk.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\twhere (dpk.ID_CAN_BO is not null and dpk.huy=0) and pk.ID_PHI_KHAC is not null and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and sv.KHOA=0 and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )\n" +
                    "\t\tunion\n" +
                    "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH,MA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_HOC_LAI_LT*SO__VHT when ID_LOAI_MON_HOC=2 then GIA_HOC_LAI_TH*SO__VHT else GIA_HOC_LAI_TT*SO__VHT end) as DON_GIA,n.ID_NHOM\n" +
                    "\t\tfrom NHOM n\n" +
                    "\t\tjoin MON_HOC mh on n.ID_MON_HOC=mh.ID_MON_HOC\n" +
                    "\t\tjoin SV_HOC_NHOM h on n.ID_NHOM=h.ID_NHOM\n" +
                    "\t\tjoin SINH_VIEN sv on h.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=n.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
                    "\t\tleft join SV_DONG_PHI_HOC_LAI dhphl on sv.ID_SINH_VIEN=dhphl.ID_SINH_VIEN\n" +
                    "\t\twhere n.HOC_LAI=1 and (dhphl.ID_CAN_BO is not null and dhphl.huy=0) and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )\n" +
                    "\t\tunion\n" +
                    "\t\tselect sv.ID_SINH_VIEN,MA_SINH_VIEN,concat(sv.HO_LOT,' ',sv.TEN) as HO_TEN,NU,NGAY_SINH,MA_LOP,TEN_LOP,(case when ID_LOAI_MON_HOC=1 then GIA_THI_LAI_LT when ID_LOAI_MON_HOC=2 then GIA_THI_LAI_TH else GIA_THI_LAI_TT end) as DON_GIA, tl.ID_THI_LAI\n" +
                    "\t\tfrom THI_LAI tl\n" +
                    "\t\tjoin SINH_VIEN sv on tl.ID_SINH_VIEN=sv.ID_SINH_VIEN\n" +
                    "\t\tleft join TINH_TRANG tt on tt.ID_TINH_TRANG=sv.ID_TINH_TRANG\n" +
                    "\t\tjoin LOP l on sv.ID_LOP=l.ID_LOP\n" +
                    "\t\tjoin KHOI k on l.ID_KHOI=k.ID_KHOI\n" +
                    "\t\tjoin HOC_PHI hp on hp.ID_HOC_KY=tl.ID_HOC_KY and hp.ID_KHOI=l.ID_KHOI\n" +
                    "\t\tjoin MON_HOC mh on mh.ID_MON_HOC=tl.ID_MON_HOC\n" +
                    "\t\twhere (tl.ID_CAN_BO is not null and tl.huy=0) and tt.HOAT_DONG=1 and (k.ID_NGANH_HOC=?1 or ?1=-1) and (k.ID_NIEN_KHOA=?2 or ?2=-1) and (l.ID_LOP=?3 or ?3=-1) and (concat(trim(sv.HO_LOT),' ',sv.TEN) like %?4% or sv.MA_SINH_VIEN LIKE %?4% )) as tbl\n" +
                    "group by ID_SINH_VIEN")
    Page<Object[]> LayDsSinhVienThuPhiPaging(Integer idNganhHoc,Integer idKhoaHoc,Integer idLop,String keyword,Pageable page);

    // ========== NEW MAINTAINABLE QUERIES ==========

    /**
     * Get students who owe regular tuition fees (HOC_PHI)
     * This replaces the first part of the complex union query
     */
    @Query(value = "SELECT sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, " +
            "CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN, " +
            "sv.NU, sv.NGAY_SINH, sv.SDT_SINH_VIEN, " +
            "CASE WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI " +
            "     ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI, " +
            "l.MA_LOP, l.TEN_LOP, hp.MUC_HOC_PHI as THANH_TIEN, sv.LA_SV_BAO_LUU " +
            "FROM SINH_VIEN sv " +
            "LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG " +
            "LEFT JOIN LOP l ON sv.ID_LOP = l.ID_LOP " +
            "LEFT JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI " +
            "LEFT JOIN HOC_PHI hp ON hp.ID_KHOI = k.ID_KHOI " +
            "LEFT JOIN SV_DONG_HP dhp ON dhp.ID_HOC_PHI = hp.ID_HOC_PHI AND dhp.ID_SINH_VIEN = sv.ID_SINH_VIEN " +
            "LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA " +
            "LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN " +
            "LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH " +
            "WHERE (dhp.ID_CAN_BO IS NULL OR dhp.huy = 1) " +
            "AND hp.ID_HOC_PHI IS NOT NULL " +
            "AND hp.MUC_HOC_PHI > 0 " +
            "AND tt.HOAT_DONG = 1 " +
            "AND sv.KHOA = 0 " +
            "AND (:idTinhTrang = -1 OR sv.ID_TINH_TRANG = :idTinhTrang) " +
            "AND (:idNganhHoc = -1 OR k.ID_NGANH_HOC = :idNganhHoc) " +
            "AND (:idKhoaHoc = -1 OR k.ID_NIEN_KHOA = :idKhoaHoc) " +
            "AND (:idLop = -1 OR l.ID_LOP = :idLop) " +
            "AND (:keyword IS NULL OR :keyword = '' OR " +
            "     CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE %:keyword% OR " +
            "     sv.MA_SINH_VIEN LIKE %:keyword%)",
            nativeQuery = true)
    List<Object[]> findStudentsWithUnpaidTuition(@Param("idNganhHoc") Integer idNganhHoc,
                                                  @Param("idKhoaHoc") Integer idKhoaHoc,
                                                  @Param("idLop") Integer idLop,
                                                  @Param("idTinhTrang") Integer idTinhTrang,
                                                  @Param("keyword") String keyword);

    /**
     * Get students who owe other fees (PHI_KHAC)
     * This replaces the second part of the complex union query
     */
    @Query(value = "SELECT sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, " +
            "CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN, " +
            "sv.NU, sv.NGAY_SINH, sv.SDT_SINH_VIEN, " +
            "CASE WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI " +
            "     ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI, " +
            "l.MA_LOP, l.TEN_LOP, " +
            "IF(pk.NGAY_KET_THUC IS NOT NULL, " +
            "   IF(CURDATE() < pk.NGAY_BAT_DAU, " +
            "      PERIOD_DIFF(DATE_FORMAT(pk.NGAY_KET_THUC,'%Y%m'), DATE_FORMAT(pk.NGAY_BAT_DAU,'%Y%m')) + 1, " +
            "      PERIOD_DIFF(DATE_FORMAT(pk.NGAY_KET_THUC,'%Y%m'), DATE_FORMAT(CURDATE(),'%Y%m')) + 1) * pk.DON_GIA, " +
            "   pk.DON_GIA) as THANH_TIEN, " +
            "sv.LA_SV_BAO_LUU " +
            "FROM SINH_VIEN sv " +
            "LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG " +
            "LEFT JOIN LOP l ON sv.ID_LOP = l.ID_LOP " +
            "LEFT JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI " +
            "LEFT JOIN PHI_KHAC pk ON pk.ID_KHOI = k.ID_KHOI " +
            "LEFT JOIN SV_DONG_PK dpk ON dpk.ID_PHI_KHAC = pk.ID_PHI_KHAC AND dpk.ID_SINH_VIEN = sv.ID_SINH_VIEN " +
            "LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA " +
            "LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN " +
            "LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH " +
            "WHERE (dpk.ID_CAN_BO IS NULL OR dpk.huy = 1) " +
            "AND pk.ID_PHI_KHAC IS NOT NULL " +
            "AND tt.HOAT_DONG = 1 " +
            "AND sv.KHOA = 0 " +
            "AND (:idTinhTrang = -1 OR sv.ID_TINH_TRANG = :idTinhTrang) " +
            "AND (:idNganhHoc = -1 OR k.ID_NGANH_HOC = :idNganhHoc) " +
            "AND (:idKhoaHoc = -1 OR k.ID_NIEN_KHOA = :idKhoaHoc) " +
            "AND (:idLop = -1 OR l.ID_LOP = :idLop) " +
            "AND (:keyword IS NULL OR :keyword = '' OR " +
            "     CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE %:keyword% OR " +
            "     sv.MA_SINH_VIEN LIKE %:keyword%) " +
            "AND (pk.NGAY_BAT_DAU IS NULL OR pk.NGAY_KET_THUC IS NULL OR " +
            "     (pk.NGAY_BAT_DAU IS NOT NULL AND pk.NGAY_KET_THUC IS NOT NULL AND CURDATE() < pk.NGAY_KET_THUC))",
            nativeQuery = true)
    List<Object[]> findStudentsWithUnpaidOtherFees(@Param("idNganhHoc") Integer idNganhHoc,
                                                    @Param("idKhoaHoc") Integer idKhoaHoc,
                                                    @Param("idLop") Integer idLop,
                                                    @Param("idTinhTrang") Integer idTinhTrang,
                                                    @Param("keyword") String keyword);

    /**
     * Get students who owe retake fees (HOC_LAI)
     * This replaces the third part of the complex union query
     */
    @Query(value = "SELECT sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, " +
            "CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN, " +
            "sv.NU, sv.NGAY_SINH, sv.SDT_SINH_VIEN, " +
            "CASE WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI " +
            "     ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI, " +
            "l.MA_LOP, l.TEN_LOP, " +
            "(CASE WHEN mh.ID_LOAI_MON_HOC = 1 THEN hp.GIA_HOC_LAI_LT * n.SO__VHT " +
            "      WHEN mh.ID_LOAI_MON_HOC = 2 THEN hp.GIA_HOC_LAI_TH * n.SO__VHT " +
            "      ELSE hp.GIA_HOC_LAI_TT * n.SO__VHT END) as THANH_TIEN, " +
            "sv.LA_SV_BAO_LUU " +
            "FROM NHOM n " +
            "JOIN MON_HOC mh ON n.ID_MON_HOC = mh.ID_MON_HOC " +
            "JOIN SV_HOC_NHOM h ON n.ID_NHOM = h.ID_NHOM " +
            "JOIN SINH_VIEN sv ON h.ID_SINH_VIEN = sv.ID_SINH_VIEN " +
            "LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG " +
            "JOIN LOP l ON sv.ID_LOP = l.ID_LOP " +
            "JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI " +
            "LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA " +
            "LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN " +
            "LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH " +
            "JOIN HOC_PHI hp ON hp.ID_HOC_KY = n.ID_HOC_KY AND hp.ID_KHOI = l.ID_KHOI " +
            "LEFT JOIN SV_DONG_PHI_HOC_LAI dhphl ON sv.ID_SINH_VIEN = dhphl.ID_SINH_VIEN AND n.ID_NHOM = dhphl.ID_NHOM " +
            "WHERE n.HOC_LAI = 1 " +
            "AND (dhphl.ID_CAN_BO IS NULL OR dhphl.huy = 1) " +
            "AND tt.HOAT_DONG = 1 " +
            "AND sv.KHOA = 0 " +
            "AND (:idTinhTrang = -1 OR sv.ID_TINH_TRANG = :idTinhTrang) " +
            "AND (:idNganhHoc = -1 OR k.ID_NGANH_HOC = :idNganhHoc) " +
            "AND (:idKhoaHoc = -1 OR k.ID_NIEN_KHOA = :idKhoaHoc) " +
            "AND (:idLop = -1 OR l.ID_LOP = :idLop) " +
            "AND (:keyword IS NULL OR :keyword = '' OR " +
            "     CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE %:keyword% OR " +
            "     sv.MA_SINH_VIEN LIKE %:keyword%)",
            nativeQuery = true)
    List<Object[]> findStudentsWithUnpaidRetakeFees(@Param("idNganhHoc") Integer idNganhHoc,
                                                     @Param("idKhoaHoc") Integer idKhoaHoc,
                                                     @Param("idLop") Integer idLop,
                                                     @Param("idTinhTrang") Integer idTinhTrang,
                                                     @Param("keyword") String keyword);

    /**
     * Get students who owe makeup exam fees (THI_LAI)
     * This replaces the fourth part of the complex union query
     */
    @Query(value = "SELECT sv.ID_SINH_VIEN, sv.MA_SINH_VIEN, " +
            "CONCAT(sv.HO_LOT, ' ', sv.TEN) as HO_TEN, " +
            "sv.NU, sv.NGAY_SINH, sv.SDT_SINH_VIEN, " +
            "CASE WHEN sv.ID_XA IS NULL THEN sv.DIA_CHI " +
            "     ELSE CONCAT(sv.DIA_CHI, ', ', dmXa.TEN_XA, ', ', dmHuyen.TEN_HUYEN, ', ', dmTinh.TEN_TINH) END as DIA_CHI, " +
            "l.MA_LOP, l.TEN_LOP, " +
            "(CASE WHEN mh.ID_LOAI_MON_HOC = 1 THEN hp.GIA_THI_LAI_LT " +
            "      WHEN mh.ID_LOAI_MON_HOC = 2 THEN hp.GIA_THI_LAI_TH " +
            "      ELSE hp.GIA_THI_LAI_TT END) as THANH_TIEN, " +
            "sv.LA_SV_BAO_LUU " +
            "FROM THI_LAI tl " +
            "JOIN SINH_VIEN sv ON tl.ID_SINH_VIEN = sv.ID_SINH_VIEN " +
            "LEFT JOIN TINH_TRANG tt ON tt.ID_TINH_TRANG = sv.ID_TINH_TRANG " +
            "JOIN LOP l ON sv.ID_LOP = l.ID_LOP " +
            "JOIN KHOI k ON l.ID_KHOI = k.ID_KHOI " +
            "LEFT JOIN DM_XA dmXa ON sv.ID_XA = dmXa.ID_XA " +
            "LEFT JOIN DM_HUYEN dmHuyen ON dmXa.ID_HUYEN = dmHuyen.ID_HUYEN " +
            "LEFT JOIN DM_TINH dmTinh ON dmHuyen.ID_TINH = dmTinh.ID_TINH " +
            "JOIN HOC_PHI hp ON hp.ID_HOC_KY = tl.ID_HOC_KY AND hp.ID_KHOI = l.ID_KHOI " +
            "JOIN MON_HOC mh ON mh.ID_MON_HOC = tl.ID_MON_HOC " +
            "WHERE (tl.ID_CAN_BO IS NULL OR tl.huy = 1) " +
            "AND tt.HOAT_DONG = 1 " +
            "AND sv.KHOA = 0 " +
            "AND (:idTinhTrang = -1 OR sv.ID_TINH_TRANG = :idTinhTrang) " +
            "AND (:idNganhHoc = -1 OR k.ID_NGANH_HOC = :idNganhHoc) " +
            "AND (:idKhoaHoc = -1 OR k.ID_NIEN_KHOA = :idKhoaHoc) " +
            "AND (:idLop = -1 OR l.ID_LOP = :idLop) " +
            "AND (:keyword IS NULL OR :keyword = '' OR " +
            "     CONCAT(TRIM(sv.HO_LOT), ' ', sv.TEN) LIKE %:keyword% OR " +
            "     sv.MA_SINH_VIEN LIKE %:keyword%)",
            nativeQuery = true)
    List<Object[]> findStudentsWithUnpaidMakeupExamFees(@Param("idNganhHoc") Integer idNganhHoc,
                                                         @Param("idKhoaHoc") Integer idKhoaHoc,
                                                         @Param("idLop") Integer idLop,
                                                         @Param("idTinhTrang") Integer idTinhTrang,
                                                         @Param("keyword") String keyword);

}
