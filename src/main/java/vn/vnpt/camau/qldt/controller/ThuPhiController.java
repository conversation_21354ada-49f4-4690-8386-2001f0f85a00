package vn.vnpt.camau.qldt.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimplePdfExporterConfiguration;
import net.sf.jasperreports.export.SimplePdfReportConfiguration;

import org.docx4j.wml.U;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import vn.vnpt.camau.qldt.ModelAttr;
import vn.vnpt.camau.qldt.Response;
import vn.vnpt.camau.qldt.Utility;
import vn.vnpt.camau.qldt.model.*;
import vn.vnpt.camau.qldt.service.*;
import vn.vnpt.camau.qldt.webservice.PublishServiceClient;
import vn.vnpt.camau.qldt.wsdl.ImportAndPublishInvResponse;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.util.*;

@Controller
@RequestMapping("/thu-phi")
public class ThuPhiController {
    private DecimalFormatSymbols dfs;
    private NumberFormat numberFormat;
    @Autowired
    private NganhHocService nganhHocSer;
    @Autowired
    private NienKhoaService nienKhoaSer;
    @Autowired
    private LopService lopSer;
    @Autowired
    private SvDongHpService svDongHpSer;
    @Autowired
    private SinhVienService sinhVienSer;
    @Autowired
    private HocPhiService hocPhiSer;
    @Autowired
    private PhiKhacService phiKhacSer;
    @Autowired
    private SvDongPkService svDongPkSer;
    @Autowired
    private NhomService nhomSer;
    @Autowired
    private SvDongPhiHocLaiService svDongPhiHocLaiSer;
    @Autowired
    private ThiLaiService thiLaiSer;
    @Autowired
    private SystemLogService logSer;

    @Autowired
    private HoaDonDienTuService hoaDonDienTuSer;

    @Autowired
    private PublishServiceClient publishServiceClient;
    @Autowired
    private ThamSoService thamSoService;
    @Autowired
    private TinhTrangService tinhTrangSer;

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        dfs = new DecimalFormatSymbols();
        dfs.setDecimalSeparator(',');
        dfs.setGroupingSeparator('.');
        numberFormat = new DecimalFormat("##,###.00", dfs);
        numberFormat.setMaximumFractionDigits(0);
        binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, numberFormat, true));
    }

    @RequestMapping("")
    public String showPage(Model model, HttpServletRequest request) {
        ModelAttr modelAttr = new ModelAttr(Utility.layNguoiDungHienTai(),
                "Thu phí",
                "sub/thu-phi",
                new String[] { "bower_components/select2/js/select2.min.js", "bower_components/jdgrid/js/jdgrid-v4.js",
                        "bower_components/jdpage/js/jdpage.js", "bower_components/print/print.min.js",
                        "js/thu-phi.js" },
                new String[] { "bower_components/select2/css/select2.min.css",
                        "bower_components/jdgrid/css/jdgrid.css" });
        model.addAttribute("MODEL", modelAttr);

        NguoiDung nd = Utility.layNguoiDungHienTai();
        String loai = nd instanceof CanBo ? "[CB]" : "[SV]";
        SystemLog log = new SystemLog();
        log.setUserAgent(Utility.getUserAgent(request));
        log.setLogDate(new Date());
        log.setIpAddress(Utility.getClientIp(request));
        log.setUserInfo(loai + " " + nd.getTenDangNhap() + " - " + nd.getHoTen());
        log.setAction("Truy cập chức năng Thu phí sinh viên");
        logSer.luu(log);

        return "layout";
    }

    @RequestMapping(value = "/init", produces = "application/json")
    @ResponseBody
    public Response init() {
        HeDaoTao hdt = Utility.layHeDaoTaoHienTai();
        List<NganhHoc> dsnh = nganhHocSer.layTheoIdHeDaoTao(hdt.getIdHeDaoTao());
        List<NienKhoa> dsnk = nienKhoaSer.layDanhSach();
        List<Lop> dsl = lopSer.layTatCaLop();
        List<TinhTrang> dsTinhTrang = tinhTrangSer.layDanhSach();

        ObjectMapper mapper = new ObjectMapper();
        ObjectNode resData = mapper.createObjectNode();

        ArrayNode nodeNh = mapper.valueToTree(dsnh);
        resData.putArray("nganhHoc").addAll(nodeNh);

        ArrayNode nodeNk = mapper.valueToTree(dsnk);
        resData.putArray("nienKhoa").addAll(nodeNk);

        ArrayNode nodeLop = mapper.valueToTree(dsl);
        resData.putArray("lop").addAll(nodeLop);

        ArrayNode nodeTinhTrang = mapper.valueToTree(dsTinhTrang);
        resData.putArray("tinhTrang").addAll(nodeTinhTrang);

        return new Response(1, resData);
    }

    @RequestMapping(value = "/lay-ds-lop", produces = "application/json")
    @ResponseBody
    public Response layDsLop(Integer idNganhHoc, Integer idKhoaHoc) {
        return new Response(1, lopSer.layAllTheoNganhHocNienKhoa(idNganhHoc, idKhoaHoc));
    }

    @RequestMapping(value = "/lay-ds-sinh-vien", produces = "application/json")
    @ResponseBody
    public Response layDsSinhVien(Integer idNganhHoc, Integer idKhoaHoc, Integer idLop, Integer idTinhTrang, String keyword, Pageable page) {
        return new Response(1, svDongHpSer.LayDsSinhVienNoPhiPaging(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword, page));
    }

    /**
     * NEW MAINTAINABLE VERSION - Test endpoint for the refactored query
     */
    @RequestMapping(value = "/lay-ds-sinh-vien-v2", produces = "application/json")
    @ResponseBody
    public Response layDsSinhVienV2(Integer idNganhHoc, Integer idKhoaHoc, Integer idLop, Integer idTinhTrang, String keyword, Pageable page) {
        return new Response(1, svDongHpSer.LayDsSinhVienNoPhiPagingV2(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword, page));
    }

    @RequestMapping(value = "/lay-ds-phi-no", produces = "application/json")
    @ResponseBody
    public Response layDsPhiNo(Integer idSinhVien) {
        return new Response(1, svDongHpSer.LayDsPhiNo(idSinhVien));
    }

    @RequestMapping(value = "/thanh-toan", produces = "application/json")
    @ResponseBody
    public Response thanhToan(Integer idSv, String noiDung, String ghiChu, String payData) {
        SinhVien sv = sinhVienSer.layTheoIdSinhVien(idSv);
        if (sv == null)
            return new Response(-3, "Không tìm thấy sinh viên, vui lòng kiểm tra lại!");

        Map<String, Object> params = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();

       
        try {
            long ttien = 0;
            long ttien_hd = 0;
            ArrayNode arrayNode = (ArrayNode) mapper.readTree(payData);
            int len = arrayNode.size();
            List<HHDVu> listHHDVu = new ArrayList<>();
            for (int i = 0; i < len; i++) {
                JsonNode node = arrayNode.get(i);
                if (node.get("chon").asBoolean()) {
                    int loaiPhi = node.get("loaiPhi").asInt();
                    int idPhi = node.get("idPhi").asInt();
                    float soLuong = (float) node.get("soLuong").asDouble();
                    float mienGiam = (float) node.get("mienGiam").asDouble();
                    if (mienGiam > 0) {
                        soLuong = soLuong - (mienGiam / 100);
                    }
                    ttien += (node.get("donGia").asLong() * soLuong);
                    long donGia = node.get("donGia").asLong();
                    String nd = node.get("noiDung").asText();
                    String dvt = node.get("dvt").asText();

                    // Hóa đơn điện tử
                    int xuatHddt = node.get("xuatHddt").asInt();
                    HHDVu hhdv = new HHDVu();
                    if (xuatHddt == 1){
                        if(mienGiam > 0){
                            soLuong = soLuong - (mienGiam/100);
                        }
                        ttien_hd += donGia * (float) soLuong;
                        long thanhtien = donGia * (long) soLuong;
                        hhdv.setTenHh(nd);
                        hhdv.setSoLuong(soLuong);
                        hhdv.setDonGia(donGia);
                        hhdv.setThanhTien(thanhtien);
                        hhdv.setDvt(dvt);
                        listHHDVu.add(hhdv);
                    }
                   
                    if (loaiPhi == 1) { // hoc phi
                        HocPhi hp = hocPhiSer.layTheoId(idPhi);
                        if (hp == null)
                            continue;
                        // return new Response(-2,"Thông tin thanh toán không đúng, vui lòng kiểm tra
                        // lại");

                        SvDongHpId id = new SvDongHpId(idSv, idPhi);

                        SvDongHp dhp = new SvDongHp();
                        dhp.setId(id);
                        dhp.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                        dhp.setDonGia(donGia);
                        dhp.setGhiChu(ghiChu);
                        dhp.setHocPhi(hp);
                        dhp.setNgayThu(new Date());
                        dhp.setNoiDungThu(nd);
                        dhp.setSinhVien(sv);
                        dhp.setSoLuong(soLuong);
                        dhp.setMienGiam(mienGiam);

                        try {
                            svDongHpSer.luu(dhp);
                            // return new Response(1,"Thanh toán thành
                            // công!",Base64.getEncoder().withoutPadding().encodeToString(genPDFReport(params)));
                        } catch (Exception e) {
                            e.printStackTrace();
                            // return new Response(-1,"Thanh toán không thành công, vui lòng thử lại sau!");
                        }
                    } else if (loaiPhi == 2) { // phi khac
                        PhiKhac pk = phiKhacSer.layTheoId(idPhi);
                        if (pk == null)
                            continue;
                        // return new Response(-2,"Thông tin thanh toán không đúng, vui lòng kiểm tra
                        // lại");

                        SvDongPkId id = new SvDongPkId(idSv, idPhi);
                        SvDongPk dpk = new SvDongPk();
                        dpk.setId(id);
                        dpk.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                        dpk.setDonGia(donGia);
                        dpk.setGhiChu(ghiChu);
                        dpk.setNoiDungThu(nd);
                        dpk.setNgayThu(new Date());
                        dpk.setPhiKhac(pk);
                        dpk.setSinhVien(sv);
                        dpk.setSoLuong(soLuong);
                        dpk.setMienGiam(mienGiam);

                        try {
                            svDongPkSer.luu(dpk);

                            // return new Response(1,"Thanh toán thành
                            // công!",Base64.getEncoder().withoutPadding().encodeToString(genPDFReport(params)));
                        } catch (Exception e) {
                            e.printStackTrace();
                            // return new Response(-1,"Thanh toán không thành công, vui lòng thử lại sau!");
                        }
                    } else if (loaiPhi == 3) { // hoc lai
                        Nhom n = nhomSer.layTheoId(idPhi);
                        if (n == null)
                            continue;
                        // return new Response(-2,"Thông tin thanh toán không đúng, vui lòng kiểm tra
                        // lại");

                        SvDongPhiHocLai dphl = new SvDongPhiHocLai();
                        dphl.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                        dphl.setGhiChu(ghiChu);
                        dphl.setNgayThu(new Date());
                        dphl.setNhom(n);
                        dphl.setNoiDung(nd);
                        dphl.setSinhVien(sv);
                        dphl.setSoLuong(soLuong);
                        dphl.setDonGia(donGia);
                        dphl.setMienGiam(mienGiam);

                        try {
                            svDongPhiHocLaiSer.luu(dphl);
                            // return new Response(1,"Thanh toán thành
                            // công!",Base64.getEncoder().withoutPadding().encodeToString(genPDFReport(params)));
                        } catch (Exception e) {
                            e.printStackTrace();
                            // return new Response(-1, "Thanh toán không thành công, vui lòng thử lại
                            // sau!");
                        }
                    } else if (loaiPhi == 4) { // thi lai
                        ThiLai tl = thiLaiSer.layTheoId(idPhi);
                        if (tl == null)
                            continue;
                        // return new Response(-2,"Thông tin thanh toán không đúng, vui lòng kiểm tra
                        // lại");

                        tl.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                        tl.setDonGia(donGia);
                        tl.setGhiChu(ghiChu);
                        tl.setNgayThu(new Date());
                        tl.setNoiDung(nd);
                        tl.setSoLuong(soLuong);
                        tl.setHuy(false);
                        tl.setMienGiam(mienGiam);

                        try {
                            thiLaiSer.luu(tl);
                        } catch (Exception e) {
                            e.printStackTrace();
                            // return new Response(-1,"Thanh toán không thành công, vui lòng thử lại sau!");
                        }
                    } /*
                       * else {
                       * return new
                       * Response(-3,"Thông tin thanh toán không đúng, vui lòng kiểm tra lại");
                       * }
                       */
                }
            }

            Integer suDungHddt = Integer.parseInt(thamSoService.layGiaTriTheoId(19));

            String tongTienChu = Utility.docTien(String.valueOf(ttien));
            // String tongTienChu_hd = Utility.docTien(String.valueOf(ttien_hd));
            if (suDungHddt == 1 && listHHDVu.size() >0){
                for (HHDVu hhdVu : listHHDVu) {
                    try {
                        List<HHDVu> listHhdvTemp = new ArrayList<>();
                        listHhdvTemp.add(hhdVu);
                        int tienHd = (int) hhdVu.getThanhTien();
                        String tienChu = Utility.docTien(String.valueOf(tienHd));
                        long tienHdTemp = (long) hhdVu.getThanhTien();
                        hoaDonDienTuSer.phatHanhHoaDonSv("Offline ",sv, listHhdvTemp, tienHdTemp, tienChu, true, null);
                        
                    } catch (Exception e) {
                        String chatIdTele = thamSoService.layGiaTriTheoId(20);
                        String tokenBotTele = thamSoService.layGiaTriTheoId(21);
//                        Utility.sendMesTelegram(e.getMessage(), chatIdTele, tokenBotTele);
                    }
                }
            }
            // hoaDonDienTuSer.phatHanhHoaDonSv("Offline ",sv, listHHDVu, ttien_hd, tongTienChu_hd, true, null);

            
            params.put("hoTen", sv.getHoTen() + " (MSSV: " + sv.getMaSinhVien() + ")");
            params.put("diaChi", sv.getLop().getTenLop());
            params.put("noiDung", noiDung);
            params.put("soTien", numberFormat.format(ttien));
            params.put("tienBangChu", tongTienChu);

            return new Response(1, "Thanh toán thành công!",
                    Base64.getEncoder().withoutPadding().encodeToString(genPDFReport(params)));
        } catch (JRException | IOException e) {
            e.printStackTrace();
        }
        return new Response(-1, "Thanh toán không thành công, vui lòng thử lại sau!");
    }

    @RequestMapping(value = "/mien-phi", produces = "application/json")
    @ResponseBody
    public Response mienPhi(Integer idSv, String payData, String ghiChu) {
        SinhVien sv = sinhVienSer.layTheoIdSinhVien(idSv);
        if (sv == null)
            return new Response(-3, "Không tìm thấy sinh viên, vui lòng kiểm tra lại!");

        ObjectMapper mapper = new ObjectMapper();

        try {
            JsonNode node = mapper.readTree(payData);

            int loaiPhi = node.get("loaiPhi").asInt();
            int idPhi = node.get("idPhi").asInt();
            float soLuong = 0f; // (float) node.get("soLuong").asDouble();
            long donGia = node.get("donGia").asLong();
            String nd = node.get("noiDung").asText();
            // String ghiChu = "Nhà trường miễn phí cho sinh viên";

            if (loaiPhi == 1) { // hoc phi
                HocPhi hp = hocPhiSer.layTheoId(idPhi);

                SvDongHpId id = new SvDongHpId(idSv, idPhi);

                SvDongHp dhp = new SvDongHp();
                dhp.setId(id);
                dhp.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                dhp.setDonGia(donGia);
                dhp.setGhiChu(ghiChu);
                dhp.setHocPhi(hp);
                dhp.setNgayThu(new Date());
                dhp.setNoiDungThu(nd);
                dhp.setSinhVien(sv);
                dhp.setSoLuong(soLuong);

                try {
                    svDongHpSer.luu(dhp);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (loaiPhi == 2) { // phi khac
                PhiKhac pk = phiKhacSer.layTheoId(idPhi);

                SvDongPkId id = new SvDongPkId(idSv, idPhi);
                SvDongPk dpk = new SvDongPk();
                dpk.setId(id);
                dpk.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                dpk.setDonGia(donGia);
                dpk.setGhiChu(ghiChu);
                dpk.setNoiDungThu(nd);
                dpk.setNgayThu(new Date());
                dpk.setPhiKhac(pk);
                dpk.setSinhVien(sv);
                dpk.setSoLuong(soLuong);

                try {
                    svDongPkSer.luu(dpk);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (loaiPhi == 3) { // hoc lai
                Nhom n = nhomSer.layTheoId(idPhi);

                SvDongPhiHocLai dphl = new SvDongPhiHocLai();
                dphl.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                dphl.setGhiChu(ghiChu);
                dphl.setNgayThu(new Date());
                dphl.setNhom(n);
                dphl.setNoiDung(nd);
                dphl.setSinhVien(sv);
                dphl.setSoLuong(soLuong);
                dphl.setDonGia(donGia);

                try {
                    svDongPhiHocLaiSer.luu(dphl);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (loaiPhi == 4) { // thi lai
                ThiLai tl = thiLaiSer.layTheoId(idPhi);

                tl.setCanBo((CanBo) Utility.layNguoiDungHienTai());
                tl.setDonGia(donGia);
                tl.setGhiChu(ghiChu);
                tl.setNgayThu(new Date());
                tl.setNoiDung(nd);
                tl.setSoLuong(soLuong);

                try {
                    thiLaiSer.luu(tl);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            return new Response(1, "Miễn phí cho SV thành công!");
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return new Response(-99, "Miễn phí cho SV không thành công, vui lòng thử lại sau!");
    }

    private byte[] genPDFReport(Map<String, Object> params) throws JRException, IOException {
        ClassPathResource classPathResource = new ClassPathResource("/reports/PhieuThu.jasper");
        ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
        JasperPrint jasperPrint = JasperFillManager.fillReport(classPathResource.getInputStream(), params);

        JRPdfExporter exporter = new JRPdfExporter();
        exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
        exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdfOutputStream));

        SimplePdfReportConfiguration reportConfig = new SimplePdfReportConfiguration();
        reportConfig.setSizePageToContent(true);
        reportConfig.setForceLineBreakPolicy(false);

        SimplePdfExporterConfiguration exportConfig = new SimplePdfExporterConfiguration();
        exportConfig.setMetadataAuthor("htdu87");
        exportConfig.setEncrypted(true);
        exportConfig.setAllowedPermissionsHint("PRINTING");
        exportConfig.setMetadataTitle("Biên lại thu tiền");

        exporter.setConfiguration(reportConfig);
        exporter.setConfiguration(exportConfig);
        exporter.exportReport();

        return pdfOutputStream.toByteArray();
    }

}
