package vn.vnpt.camau.qldt.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Service;
import vn.vnpt.camau.qldt.model.SvDongHp;
import vn.vnpt.camau.qldt.model.SvDongHpId;
import vn.vnpt.camau.qldt.repository.SvDongHpRepository;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SvDongHpServiceImp implements SvDongHpService {
    @Autowired
    private SvDongHpRepository svDongHpRepo;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<Object[]> LayDsPhiNo(Integer idSinhVien) {
        return svDongHpRepo.LayDsPhiNo(idSinhVien,-1,-1);
    }

    @Override
    public List<Object[]> LayDsPhiNo(Integer idSinhVien,Integer idNienKhoa, Integer idHocKy) {
        return svDongHpRepo.LayDsPhiNo(idSinhVien,idNienKhoa, idHocKy);
    }

    @Override
    public Page<Object[]> LayDsSinhVienNoPhiPaging(Integer idNganhHoc, Integer idKhoaHoc, Integer idLop, Integer idTinhTrang, String keyword, Pageable page) {
        // Use the original complex query for now, but we'll create a new maintainable version
        return svDongHpRepo.LayDsSinhVienNoPhiPaging(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword, page);
    }

    /**
     * NEW MAINTAINABLE VERSION: Get students who owe fees with better structure
     * This method breaks down the complex query into smaller, manageable parts
     */
    public Page<Object[]> LayDsSinhVienNoPhiPagingV2(Integer idNganhHoc, Integer idKhoaHoc, Integer idLop, Integer idTinhTrang, String keyword, Pageable page) {
        // This is a more maintainable approach using separate queries and combining results

        try {
            // Get students from each fee type using separate, focused queries
            List<Object[]> tuitionFees = svDongHpRepo.findStudentsWithUnpaidTuition(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword);
            List<Object[]> otherFees = svDongHpRepo.findStudentsWithUnpaidOtherFees(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword);
            List<Object[]> retakeFees = svDongHpRepo.findStudentsWithUnpaidRetakeFees(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword);
            List<Object[]> makeupExamFees = svDongHpRepo.findStudentsWithUnpaidMakeupExamFees(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword);

            // Combine and aggregate results by student ID
            Map<Integer, StudentFeeInfo> studentMap = new HashMap<>();

            // Process each fee type
            processFeeList(tuitionFees, studentMap, "TUITION");
            processFeeList(otherFees, studentMap, "OTHER");
            processFeeList(retakeFees, studentMap, "RETAKE");
            processFeeList(makeupExamFees, studentMap, "MAKEUP_EXAM");

            // Convert to final result format and apply pagination
            List<Object[]> allResults = studentMap.values().stream()
                .map(this::convertToObjectArray)
                .collect(Collectors.toList());

            // Apply manual pagination since we're combining multiple queries
            return applyPagination(allResults, page);

        } catch (Exception e) {
            // Fallback to original method if new implementation fails
            System.err.println("Error in new implementation, falling back to original: " + e.getMessage());
            return LayDsSinhVienNoPhiPaging(idNganhHoc, idKhoaHoc, idLop, idTinhTrang, keyword, page);
        }
    }

    /**
     * Helper method to process fee lists and aggregate by student
     */
    private void processFeeList(List<Object[]> feeList, Map<Integer, StudentFeeInfo> studentMap, String feeType) {
        for (Object[] row : feeList) {
            Integer studentId = (Integer) row[0];
            StudentFeeInfo info = studentMap.computeIfAbsent(studentId, k -> new StudentFeeInfo(row));

            // Add the fee amount to total
            Double feeAmount = row[9] != null ? ((Number) row[9]).doubleValue() : 0.0;
            info.addFee(feeAmount, feeType);
        }
    }

    /**
     * Convert StudentFeeInfo to Object array for compatibility
     */
    private Object[] convertToObjectArray(StudentFeeInfo info) {
        return new Object[] {
            info.getIdSinhVien(),
            info.getMaSinhVien(),
            info.getHoTen(),
            info.getNu(),
            info.getNgaySinh(),
            info.getSdtSinhVien(),
            info.getDiaChi(),
            info.getMaLop(),
            info.getTenLop(),
            info.getTongTien(),
            info.getLaSvBaoLuu()
        };
    }

    @Override
    public List<Object[]> traCuuLsNopPhi(Integer idSinhVien, Date from, Date to) {
        return svDongHpRepo.TraCuuLsNopPhi(idSinhVien,from,to);
    }

    @Override
    public SvDongHp luu(SvDongHp svDongHp) {
        return svDongHpRepo.save(svDongHp);
    }

	@Override
	public List<Object[]> LayDsPhiThu(Integer idSinhVien) {
		return svDongHpRepo.LayDsPhiThu(idSinhVien,-1,-1);
	}

	@Override
	public List<Object[]> LayDsPhiThu(Integer idSinhVien, Integer idNienKhoa, Integer idHocKy) {
		return svDongHpRepo.LayDsPhiThu(idSinhVien,idNienKhoa, idHocKy);
	}

	@Override
	public Page<Object[]> LayDsSinhVienThuPhiPaging(Integer idNganhHoc, Integer idKhoaHoc, Integer idLop,
			String keyword, Pageable page) {
		return svDongHpRepo.LayDsSinhVienThuPhiPaging(idNganhHoc, idKhoaHoc, idLop, keyword, page);
	}

	@Override
	public void xoa(SvDongHpId id) {
		svDongHpRepo.deleteById(id);
	}

	@Override
	public SvDongHp layTheoId(SvDongHpId id) {
		return svDongHpRepo.findById(id).get();
	}

    /**
     * Apply pagination to a list of results
     */
    private Page<Object[]> applyPagination(List<Object[]> allResults, Pageable pageable) {
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), allResults.size());

        List<Object[]> pageContent = start >= allResults.size() ?
            Collections.emptyList() : allResults.subList(start, end);

        return new PageImpl<>(pageContent, pageable, allResults.size());
    }

    /**
     * Helper class to aggregate student fee information
     */
    private static class StudentFeeInfo {
        private Integer idSinhVien;
        private String maSinhVien;
        private String hoTen;
        private Boolean nu;
        private Date ngaySinh;
        private String sdtSinhVien;
        private String diaChi;
        private String maLop;
        private String tenLop;
        private Double tongTien = 0.0;
        private Boolean laSvBaoLuu;
        private Set<String> feeTypes = new HashSet<>();

        public StudentFeeInfo(Object[] row) {
            this.idSinhVien = (Integer) row[0];
            this.maSinhVien = (String) row[1];
            this.hoTen = (String) row[2];
            this.nu = (Boolean) row[3];
            this.ngaySinh = (Date) row[4];
            this.sdtSinhVien = (String) row[5];
            this.diaChi = (String) row[6];
            this.maLop = (String) row[7];
            this.tenLop = (String) row[8];
            this.laSvBaoLuu = row.length > 10 ? (Boolean) row[10] : false;
        }

        public void addFee(Double amount, String feeType) {
            if (amount != null && !feeTypes.contains(feeType)) {
                this.tongTien += amount;
                this.feeTypes.add(feeType);
            }
        }

        // Getters
        public Integer getIdSinhVien() { return idSinhVien; }
        public String getMaSinhVien() { return maSinhVien; }
        public String getHoTen() { return hoTen; }
        public Boolean getNu() { return nu; }
        public Date getNgaySinh() { return ngaySinh; }
        public String getSdtSinhVien() { return sdtSinhVien; }
        public String getDiaChi() { return diaChi; }
        public String getMaLop() { return maLop; }
        public String getTenLop() { return tenLop; }
        public Double getTongTien() { return tongTien; }
        public Boolean getLaSvBaoLuu() { return laSvBaoLuu; }
    }

}
