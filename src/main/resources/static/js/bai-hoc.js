var pref_url="/bai-hoc";
$(document).ready(function(){
	$('#grid-ds').jdGrid({
		columns:[
			{name:'ma<PERSON>ai<PERSON><PERSON>',title:'<PERSON><PERSON> bài học',css:{'text-align':'center'}},
			{name:'ten<PERSON>aiH<PERSON>',title:'Tên bài học'},
			{name:'tenMonHoc',title:'<PERSON><PERSON><PERSON> học'},
			{name:'hinh<PERSON>hu<PERSON>',title:'<PERSON><PERSON><PERSON> thức',css:{'text-align':'center'}},
			{name:'thuTu',title:'Thứ tự',css:{'text-align':'center'}},
			{name:'soTietLt',title:'Tiết LT',css:{'text-align':'center'}},
			{name:'soTietTh',title:'Tiết TH',css:{'text-align':'center'}},
			{name:'col2',title:'<PERSON><PERSON>Tác',type:'control',css:{'text-align':'center','width':'80px'},content:function(obj){
				return '<a href="#" class="cmd cmd-edit" rid="'+obj.idBaiHoc+'" data-target="#addModal" data-toggle="modal" title="Sửa"><i class="fa fa-edit"></i></a>'
				+'<a href="#" class="row-del-1 cmd cmd-del" rid="'+obj.idBaiHoc+'"data-target="#delModal" data-toggle="modal" title="Xóa"><i class="fa fa-trash text-danger"></i></a>';
				}}
		],
		height:'431px',
        extclass:'tbl-primary',
        shwno:true,
        nocss:{'text-align':'center','width':'50px'},
        nolabel:'STT'

	});
	 $('.select2').select2();
	$('#page-ds').jdPage({
		onPageChanged:function(p){
			timKiem(p-1);
		},
		onRowOnPageChanged:function(num) {
            timKiem(0);
        }
	});

	$('#baiHocForm').submit(function(e){
		e.preventDefault();
		luu();
	});
	$('#txt-keyword').keypress(function(e) {
        var keycode = (event.keyCode ? event.keyCode : event.which);
        if(keycode == '13') {
            e.preventDefault();
            timKiem(0);
        }
    });

	 $('#btn-search').click(function() {
	        timKiem(0);
	    });
	 $('#cmb-monHoc-search').change(function(){
		 timKiem(0);
	    });

	 // Auto-calculate total periods
	 $('#soTietLt, #soTietTh').on('input', function() {
	     calculateTotalPeriods();
	 });

	 layDsMonHoc();
	 $('#addBaiHocModal').on('shown.bs.modal', function (e) {
		 $('#modalTitle').text('Thêm bài học');
		 clear();
		 if(typeof resetForm === 'function') {
			resetForm('#baiHocForm');
		 }
	   })
});

function layDsMonHoc() {
    $.ajax({
    	url:pref_url+'/init',
        method:'get',
        beforeSend:function() {
            showBoxLoading('box-frm');
        }, success:function(res) {
            	 // Populate search dropdown
            	 $('#cmb-monHoc-search').find('option').remove();
            	 $('#cmb-monHoc-search').append($('<option>', {
	                    value: -1,
	                    text : 'Tất cả'
	                }));
            	 $.each(res.resData['monHoc'],function(i,obj){
                     $('#cmb-monHoc-search').append($('<option>', {
                         value: obj.idMonHoc,
                         text : obj.tenMonHoc
                     }));
                 });
                 $('#cmb-monHoc-search').trigger('change');

                 // Populate modal dropdown
                 $('#cmb-monHoc').find('option').remove();
            	 $.each(res.resData['monHoc'],function(i,obj){
                     $('#cmb-monHoc').append($('<option>', {
                         value: obj.idMonHoc,
                         text : obj.tenMonHoc
                     }));
                 });
                 $('#cmb-monHoc').trigger('change');
                 timKiem(0);
        }, error:function(jqXHR) {
            showErr('Thông báo', 'Đã có lỗi xảy ra, vui lòng thử lại sau');
        }, complete:function() {
            hideBoxLoading('box-frm');
        }
    });
}

function timKiem(page) {
    var data=new FormData($('#frmSearch')[0]);
    data.append('page',page);

    // Get page size, default to 20 if pagination not initialized yet
    var pageSize = 20;
    var jdPageData = $('#page-ds').data('jdpage');
    if (jdPageData && typeof jdPageData.getRowOnPage === 'function') {
        pageSize = jdPageData.getRowOnPage();
    }
    data.append('size', pageSize);

    // Debug: log the search parameters
    console.log('Searching with parameters:', {
        idMonHoc: data.get('idMonHoc'),
        hinhThuc: data.get('hinhThuc'),
        search: data.get('search'),
        page: data.get('page'),
        size: data.get('size')
    });

    $.ajax({
        url:pref_url+'/tim-kiem',
        method:'post',
        data:data,
        processData:false,
        contentType:false,
        beforeSend:function() {
            showBoxLoading('box-frm');
        }, success:function(res) {
            console.log('Search response:', res);
            var jdGrid=$('#grid-ds').data('jdgrid');
            if (jdGrid) {
                jdGrid.setPage(res.resData.number+1);
                jdGrid.setRowOnPage(res.resData.size);
                jdGrid.fillData(res.resData.content);
            }

            var jdPage = $('#page-ds').data('jdpage');
            if (jdPage) {
                jdPage.setData({totalPage:res.resData.totalPages,currentPage:res.resData.number+1,totalItem:res.resData.totalElements,itemOnPage:res.resData.size});
            }
            $('.cmd-edit').click(function(e){
                e.preventDefault();
                $('#modalTitle').text('Sửa bài học');
                layChiTiet($(this).attr('rid'));
            });
            $('.cmd-del').click(function(e){
                e.preventDefault();
                var id = $(this).attr('rid');
                showCfm('Xác nhận', 'Bạn chắc muốn xóa?', function(){
                    xoa(id);
                });
            });
        }, error:function(jqXHR) {
            showErr('Thông báo', 'Đã có lỗi xảy ra, vui lòng thử lại sau');
        }, complete:function() {
            hideBoxLoading('box-frm');
        }
    });
}

function layChiTiet(id){
	$.ajax({
		url:pref_url+'/lay-chi-tiet',
		method:'get',
		dataType:'json',
		data:{idBaiHoc:id},
		beforeSend:function(){
            showBoxLoading('box-frm');
		},
		success:function(res){
			if(res.resCode > 0) {
				var data = res.resData;
				$('#idBaiHoc').val(data.idBaiHoc);
				$('#maBaiHoc').val(data.maBaiHoc);
				$('#maBaiHoc').prop('readonly', true);
				$('#tenBaiHoc').val(data.tenBaiHoc);
				var idMonHoc = data.monHoc ? data.monHoc.idMonHoc : data.idMonHoc;
				$('#cmb-monHoc').val(idMonHoc).trigger('change');
				$('#hinhThuc').val(data.hinhThuc).trigger('change');
				$('#soTietLt').val(data.soTietLt);
				$('#soTietTh').val(data.soTietTh);
				$('#thuTu').val(data.thuTu);
				$('#noiDung').val(data.noiDung);
				calculateTotalPeriods();
			} else {
				showErr('Thông báo', res.resMsg);
			}
		},
		error:function(){
			showErr('Thông báo','Lấy dữ liệu không thành công, vui lòng thử lại sau!');
		},
		complete:function(){
            hideBoxLoading('box-frm');
		}
	});
}

function luu(){
	// Collect form data as JSON object
	var baiHoc = {
		idBaiHoc: $('#idBaiHoc').val() || null,
		maBaiHoc: $('#maBaiHoc').val(),
		tenBaiHoc: $('#tenBaiHoc').val(),
		hinhThuc: $('#hinhThuc').val(),
		soTietLt: parseInt($('#soTietLt').val()) || 0,
		soTietTh: parseInt($('#soTietTh').val()) || 0,
		thuTu: parseInt($('#thuTu').val()) || null,
		noiDung: $('#noiDung').val(),
		monHoc: {
			idMonHoc: parseInt($('#cmb-monHoc').val())
		}
	};

	$.ajax({
		url:pref_url+'/luu',
		method:'post',
        contentType:'application/json',
        data: JSON.stringify(baiHoc),
        beforeSend:function(){
            showBoxLoading('box-add');
		},success:function(res){
			if(res.resCode>0){
				$('#addBaiHocModal').modal('hide');
				showToastSuc(res.resMsg);
				timKiem(0);
			}else{
				 showErr('Thông báo',res.resMsg);
			}
		},error:function(jqXHR){
            showErr('Thông báo', 'Đã có lỗi xảy ra, vui lòng thử lại sau');
		},complete:function(){
            hideBoxLoading('box-add');
		}
	});
}

function clear(){
	$('#idBaiHoc').val('');
	$('#maBaiHoc').val('');
	$('#tenBaiHoc').val('');
	$('#cmb-monHoc').val($("#cmb-monHoc option:first").val()).trigger('change');
	$('#hinhThuc').val($("#hinhThuc option:first").val()).trigger('change');
	$('#soTietLt').val('');
	$('#soTietTh').val('');
	$('#thuTu').val('');
	$('#noiDung').val('');
	$('#maBaiHoc').prop('readonly', false);
	$('#grid-ds').data('jdgrid').clearSelectedRow();
}

function xoa(id){
	$.ajax({
		url:pref_url+'/xoa',
		method:'post',
		dataType:'json',
		data:{idBaiHoc:id},
		beforeSend:function(){
			showBoxLoading('box-frm');
		},
		success:function(res){
			if(res.resCode>0){
                showToastSuc(res.resMsg);
                timKiem(0);
			}else{
                showErr('Thông báo',res.resMsg);
			}
		},
		error:function(jqXHR){
			 showErr('Thông báo','Đã có lỗi xảy ra, vui lòng thử lại sau');
		},
		complete:function(){
            hideBoxLoading('box-frm');
		}
	});
}

// Auto-generate lesson code
function taoMaTuDong() {
    var idMonHoc = $('#cmb-monHoc').val();
    if (!idMonHoc) {
        showErr('Thông báo', 'Vui lòng chọn môn học trước');
        return;
    }

    $.ajax({
        url: pref_url + '/tao-ma-bai-hoc-tu-dong',
        type: 'GET',
        data: { idMonHoc: idMonHoc },
        beforeSend: function() {
            showBoxLoading('box-add');
        },
        success: function(response) {
            if (response.resCode === 1) {
                $('#maBaiHoc').val(response.resData);
                showToastSuc('Đã tạo mã bài học tự động: ' + response.resData);
            } else {
                showErr('Thông báo', 'Lỗi tạo mã tự động: ' + response.resMsg);
            }
        },
        error: function() {
            showErr('Thông báo', 'Không thể tạo mã tự động');
        },
        complete: function() {
            hideBoxLoading('box-add');
        }
    });
}

// Calculate total periods
function calculateTotalPeriods() {
    var soTietLt = parseInt($('#soTietLt').val()) || 0;
    var soTietTh = parseInt($('#soTietTh').val()) || 0;
    var tongSoTiet = soTietLt + soTietTh;
    $('#tongSoTiet').val(tongSoTiet);
}

// Reset filter form
function resetFilter() {
    $('#cmb-monHoc-search').val(-1).trigger('change');
    $('#cmb-hinhThuc-search').val('').trigger('change');
    $('#txt-keyword').val('');
    timKiem(0);
}
